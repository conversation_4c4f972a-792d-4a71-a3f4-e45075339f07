# Hydra-Virus.py
"Cut off one head, two more shall take its place!"

`Hydra.py` is a script that displays a message box. When you close a message box, 2 more will show up.

## Usage

`python Hydra.py`

## Requirements

- Python 3
- Tkinter (installed by default with Python 3)
- Pillow (to package the script into an executable with icon)

## Packaging

`pyinstaller --onefile --noconsole --disable-windowed-traceback --icon dragon-svgrepo-com.png Hydra.py`

Icon: https://www.svgrepo.com/svg/307019/dragon
